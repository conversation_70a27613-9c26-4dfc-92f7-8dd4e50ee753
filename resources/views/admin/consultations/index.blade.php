@extends('components.layouts.admin')

@section('title', __('Manage Consultations'))

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Consultations') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Consultations</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Filters and Search -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('Filter Consultations') }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.consultations.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus mr-2"></i>
                            {{ __('New Consultation') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.consultations.index') }}" method="GET" class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="search">{{ __('Search') }}</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    </div>
                                    <input type="text" class="form-control" id="search" name="search" placeholder="Search by name or email" value="{{ request('search') }}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="status">{{ __('Status') }}</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="">{{ __('All Statuses') }}</option>
                                    @foreach($statuses as $status)
                                        <option value="{{ $status->id }}" {{ request('status') == $status->id ? 'selected' : '' }}>
                                            {{ $status->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="date">{{ __('Date Range') }}</label>
                                <select class="form-control" id="date" name="date">
                                    <option value="">{{ __('All Time') }}</option>
                                    <option value="today" {{ request('date') == 'today' ? 'selected' : '' }}>{{ __('Today') }}</option>
                                    <option value="week" {{ request('date') == 'week' ? 'selected' : '' }}>{{ __('This Week') }}</option>
                                    <option value="month" {{ request('date') == 'month' ? 'selected' : '' }}>{{ __('This Month') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-filter mr-2"></i>{{ __('Filter') }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Consultations Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('All Consultations') }}</h3>
                    <div class="card-tools">
                        <span class="badge badge-primary">{{ $consultations->total() }} {{ __('Total') }}</span>
                    </div>
                </div>
                <!-- /.card-header -->
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>{{ __('Name') }}</th>
                                <th>{{ __('Email') }}</th>
                                <th>{{ __('Date') }}</th>
                                <th>{{ __('Status') }}</th>
                                <th>{{ __('Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($consultations as $consultation)
                                <tr>
                                    <td><strong>{{ $consultation->name }}</strong></td>
                                    <td>{{ $consultation->email }}</td>
                                    <td>{{ $consultation->created_at->format('M d, Y') }}</td>
                                    <td>
                                        @if ($consultation->status)
                                            <span class="badge" style="background-color: {{ $consultation->status->color }};">
                                                {{ $consultation->status->name }}
                                            </span>
                                        @else
                                            <span class="badge badge-secondary">{{ __('No Status') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('admin.consultations.show', $consultation) }}" class="btn btn-sm btn-info" data-toggle="tooltip" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.consultations.edit', $consultation) }}" class="btn btn-sm btn-warning" data-toggle="tooltip" title="Edit">
                                                <i class="fas fa-pencil-alt"></i>
                                            </a>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false" title="Update Status">
                                                    <i class="fas fa-tag"></i>
                                                </button>
                                                <div class="dropdown-menu">
                                                    @foreach($statuses as $status)
                                                        @if(!$consultation->status || $consultation->status->id != $status->id)
                                                            <form action="{{ route('admin.consultations.update-status', $consultation) }}" method="POST" class="d-inline">
                                                                @csrf
                                                                @method('PATCH')
                                                                <input type="hidden" name="status_id" value="{{ $status->id }}">
                                                                <button type="submit" class="dropdown-item">
                                                                    <span class="badge mr-2" style="background-color: {{ $status->color }};">
                                                                        &nbsp;
                                                                    </span>
                                                                    {{ $status->name }}
                                                                </button>
                                                            </form>
                                                        @endif
                                                    @endforeach
                                                </div>
                                            </div>
                                            <form action="{{ route('admin.consultations.destroy', $consultation) }}" method="POST" class="d-inline" onsubmit="return confirm('{{ __('Are you sure you want to delete this consultation?') }}')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" data-toggle="tooltip" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-center py-5">
                                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                            <h5>{{ __('No consultations found') }}</h5>
                                            <p class="text-muted">{{ __('No consultation requests match your criteria') }}</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                <!-- /.card-body -->
                @if($consultations->hasPages())
                    <div class="card-footer clearfix">
                        {{ $consultations->links() }}
                    </div>
                @endif
            </div>
            <!-- /.card -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
