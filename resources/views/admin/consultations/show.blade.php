@extends('components.layouts.admin') :title="__('View Consultation')">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">{{ __('View Consultation') }}</h1>
            <p class="text-muted small">{{ __('Consultation details from') }} {{ $consultation->name }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.consultations.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                {{ __('Back') }}
            </a>
            <a href="{{ route('admin.consultations.edit', $consultation) }}" class="btn btn-primary">
                <i class="fas fa-pencil-alt me-2"></i>
                {{ __('Edit') }}
            </a>
        </div>
    </div>

    <div class="card border-0 shadow-sm">
        <div class="card-body p-4">
            <div class="row">
                <div class="col-md-6">
                    <h3 class="h5 mb-3">{{ __('Contact Information') }}</h3>
                    <div class="mb-4">
                        <div class="mb-3">
                            <label class="form-label fw-medium">{{ __('Name') }}</label>
                            <div>{{ $consultation->name }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-medium">{{ __('Email') }}</label>
                            <div>{{ $consultation->email }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-medium">{{ __('Website URL') }}</label>
                            <div>
                                @if ($consultation->website_url)
                                    <a href="{{ $consultation->website_url }}" target="_blank" class="text-primary">{{ $consultation->website_url }}</a>
                                @else
                                    <span class="text-muted">{{ __('Not provided') }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h3 class="h5 mb-3">{{ __('Status Information') }}</h3>
                    <div class="mb-4">
                        <div class="mb-3">
                            <label class="form-label fw-medium">{{ __('Status') }}</label>
                            <div class="d-flex align-items-center">
                                @if ($consultation->status)
                                    <span class="badge me-3" style="background-color: {{ $consultation->status->color }};">
                                        {{ $consultation->status->name }}
                                    </span>
                                @else
                                    <span class="badge bg-secondary me-3">{{ __('No Status') }}</span>
                                @endif

                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="statusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ __('Change Status') }}
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="statusDropdown">
                                        @foreach($statuses as $status)
                                            @if(!$consultation->status || $consultation->status->id != $status->id)
                                                <li>
                                                    <form action="{{ route('admin.consultations.update-status', $consultation) }}" method="POST">
                                                        @csrf
                                                        @method('PATCH')
                                                        <input type="hidden" name="status_id" value="{{ $status->id }}">
                                                        <button type="submit" class="dropdown-item">
                                                            <span class="badge me-2" style="background-color: {{ $status->color }};">
                                                                &nbsp;
                                                            </span>
                                                            {{ $status->name }}
                                                        </button>
                                                    </form>
                                                </li>
                                            @endif
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                            @if ($consultation->status && $consultation->status->description)
                                <div class="form-text">{{ $consultation->status->description }}</div>
                            @endif
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-medium">{{ __('Submitted On') }}</label>
                            <div>{{ $consultation->created_at->format('F j, Y \a\t g:i A') }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <hr class="my-4">

            <h3 class="h5 mb-3">{{ __('Business Information') }}</h3>
            <div class="mb-4">
                <div class="mb-3">
                    <label class="form-label fw-medium">{{ __('Business Description') }}</label>
                    <div class="p-3 bg-light rounded">{{ $consultation->business_description }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-medium">{{ __('Pain Points') }}</label>
                    <div class="p-3 bg-light rounded">{{ $consultation->pain_point }}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-medium">{{ __('Current Automation') }}</label>
                    <div class="p-3 bg-light rounded">{{ $consultation->current_automation }}</div>
                </div>
                @if ($consultation->additional_notes)
                    <div class="mb-3">
                        <label class="form-label fw-medium">{{ __('Additional Notes') }}</label>
                        <div class="p-3 bg-light rounded">{{ $consultation->additional_notes }}</div>
                    </div>
                @endif
            </div>
        </div>
        <div class="card-footer bg-white d-flex justify-content-between py-3">
            <form action="{{ route('admin.consultations.destroy', $consultation) }}" method="POST" onsubmit="return confirm('{{ __('Are you sure you want to delete this consultation?') }}')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash me-2"></i>
                    {{ __('Delete Consultation') }}
                </button>
            </form>
            <a href="{{ route('admin.consultations.index') }}" class="btn btn-outline-secondary">
                {{ __('Back to List') }}
            </a>
        </div>
    </div>
@endsection
