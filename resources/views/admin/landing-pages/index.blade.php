@extends('layouts.admin')

@section('title', 'Landing Pages')

@section('content')
<div class="container-fluid px-4 py-4">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1 text-dark fw-bold">Landing Pages</h1>
            <p class="text-muted mb-0">Manage your landing pages and track their performance</p>
        </div>
        <a href="{{ route('admin.landing-pages.create') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>Create Landing Page
        </a>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show border-0 shadow-sm" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Landing Pages Table -->
    @if($landingPages->count() > 0)
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="border-0 fw-semibold text-dark ps-4">Landing Page</th>
                                <th class="border-0 fw-semibold text-dark">Status</th>
                                <th class="border-0 fw-semibold text-dark">Leads</th>
                                <th class="border-0 fw-semibold text-dark">Quiz</th>
                                <th class="border-0 fw-semibold text-dark">Created</th>
                                <th class="border-0 fw-semibold text-dark">URL</th>
                                <th class="border-0 fw-semibold text-dark pe-4">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($landingPages as $landingPage)
                                <tr>
                                    <td class="ps-4">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                <i class="fas fa-file-alt text-white"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-bold text-dark">{{ $landingPage->title }}</h6>
                                                @if($landingPage->meta_description)
                                                    <p class="text-muted small mb-0">{{ Str::limit($landingPage->meta_description, 60) }}</p>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center gap-2">
                                            <span class="badge {{ $landingPage->is_active ? 'bg-success' : 'bg-secondary' }} px-2 py-1">
                                                {{ $landingPage->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="h6 mb-0 text-primary fw-bold">{{ $landingPage->leads_count }}</span>
                                            <span class="text-muted ms-1">leads</span>
                                        </div>
                                    </td>
                                    <td>
                                        @if($landingPage->has_quiz)
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-info px-2 py-1 me-2">Quiz</span>
                                                <span class="text-muted small">{{ $landingPage->quiz ? $landingPage->quiz->questions->count() : 0 }} questions</span>
                                            </div>
                                        @else
                                            <span class="text-muted">No quiz</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="text-muted">
                                            <div class="fw-semibold">{{ $landingPage->created_at->format('M j, Y') }}</div>
                                            <div class="small">{{ $landingPage->created_at->diffForHumans() }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <code class="small text-muted">/landing/{{ Str::limit($landingPage->slug, 15) }}</code>
                                    </td>
                                    <td class="pe-4">
                                        <div class="d-flex align-items-center gap-2">
                                            <a href="{{ route('admin.landing-pages.show', $landingPage) }}" class="btn btn-sm btn-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown" title="More Actions">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="{{ route('admin.landing-pages.edit', $landingPage) }}">
                                                        <i class="fas fa-edit me-2"></i>Edit Page
                                                    </a></li>
                                                    @if($landingPage->has_quiz)
                                                        <li><a class="dropdown-item" href="{{ route('admin.quizzes.show', $landingPage) }}">
                                                            <i class="fas fa-question-circle me-2"></i>Manage Quiz
                                                        </a></li>
                                                    @endif
                                                    <li><a class="dropdown-item" href="{{ route('landing-pages.show', $landingPage) }}" target="_blank">
                                                        <i class="fas fa-external-link-alt me-2"></i>Preview
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $landingPage->id }}">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal{{ $landingPage->id }}" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Delete Landing Page</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete the landing page "<strong>{{ $landingPage->title }}</strong>"?</p>
                                                <p class="text-danger"><small>This action cannot be undone and will also delete all associated leads and quiz data.</small></p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <form method="POST" action="{{ route('admin.landing-pages.destroy', $landingPage) }}" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger">Delete</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        @if($landingPages->hasPages())
            <div class="d-flex justify-content-center mt-4">
                {{ $landingPages->links() }}
            </div>
        @endif
    @else
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="fas fa-file-alt fa-4x text-muted"></i>
            </div>
            <h4 class="text-muted mb-3">No Landing Pages Yet</h4>
            <p class="text-muted mb-4">Create your first landing page to start generating leads and building your conversion funnel.</p>
            <a href="{{ route('admin.landing-pages.create') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Create Your First Landing Page
            </a>
        </div>
    @endif

    <!-- Statistics Cards -->
    <div class="row g-4 mt-5">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient rounded-3 p-3">
                                <i class="fas fa-file-alt text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small text-uppercase fw-semibold">Total Pages</div>
                            <div class="h4 mb-0 text-dark fw-bold">{{ $landingPages->total() }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-3 p-3">
                                <i class="fas fa-check-circle text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small text-uppercase fw-semibold">Active Pages</div>
                            <div class="h4 mb-0 text-dark fw-bold">{{ $landingPages->where('is_active', true)->count() }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient rounded-3 p-3">
                                <i class="fas fa-question-circle text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small text-uppercase fw-semibold">With Quiz</div>
                            <div class="h4 mb-0 text-dark fw-bold">{{ $landingPages->where('has_quiz', true)->count() }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient rounded-3 p-3">
                                <i class="fas fa-users text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small text-uppercase fw-semibold">Total Leads</div>
                            <div class="h4 mb-0 text-dark fw-bold">{{ $landingPages->sum('leads_count') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
