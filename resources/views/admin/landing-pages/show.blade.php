@extends('layouts.admin')

@section('title', $landingPage->title)

@section('content')
<div class="container-fluid px-4 py-4">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-start mb-4">
        <div>
            <div class="d-flex align-items-center mb-2">
                <div class="me-3">
                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                        <i class="fas fa-file-alt text-white fa-lg"></i>
                    </div>
                </div>
                <div>
                    <h1 class="h3 mb-1 text-dark fw-bold">{{ $landingPage->title }}</h1>
                    <div class="d-flex align-items-center gap-3">
                        <span class="badge {{ $landingPage->is_active ? 'bg-success' : 'bg-secondary' }} px-3 py-2">
                            <i class="fas fa-{{ $landingPage->is_active ? 'check-circle' : 'pause-circle' }} me-1"></i>
                            {{ $landingPage->is_active ? 'Active' : 'Inactive' }}
                        </span>
                        <span class="badge {{ $landingPage->has_quiz ? 'bg-info' : 'bg-secondary' }} px-3 py-2">
                            <i class="fas fa-question-circle me-1"></i>
                            Quiz {{ $landingPage->has_quiz ? 'Enabled' : 'Disabled' }}
                        </span>
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            Created {{ $landingPage->created_at->format('M j, Y') }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('landing-pages.show', $landingPage) }}" target="_blank" class="btn btn-outline-primary">
                <i class="fas fa-external-link-alt me-2"></i>View Live
            </a>
            <a href="{{ route('admin.landing-pages.edit', $landingPage) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Page
            </a>
            <a href="{{ route('admin.landing-pages.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back
            </a>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show border-0 shadow-sm" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Statistics Cards -->
    <div class="row g-4 mb-5">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient rounded-3 p-3">
                                <i class="fas fa-users text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small text-uppercase fw-semibold">Total Leads</div>
                            <div class="h4 mb-0 text-dark fw-bold">{{ $stats['total_leads'] }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-3 p-3">
                                <i class="fas fa-calendar-day text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small text-uppercase fw-semibold">Today</div>
                            <div class="h4 mb-0 text-dark fw-bold">{{ $stats['leads_today'] }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient rounded-3 p-3">
                                <i class="fas fa-calendar-week text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small text-uppercase fw-semibold">This Week</div>
                            <div class="h4 mb-0 text-dark fw-bold">{{ $stats['leads_this_week'] }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient rounded-3 p-3">
                                <i class="fas fa-chart-line text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small text-uppercase fw-semibold">Avg Quiz Score</div>
                            <div class="h4 mb-0 text-dark fw-bold">{{ $stats['avg_quiz_score'] ? number_format($stats['avg_quiz_score'], 1) : 'N/A' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row g-4">
        <div class="col-lg-8">
            <!-- Page Details -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="card-title mb-0 fw-bold text-dark">
                        <i class="fas fa-info-circle me-2 text-primary"></i>Page Details
                    </h5>
                </div>
                <div class="card-body pt-3">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <div class="bg-light rounded-2 p-2">
                                        <i class="fas fa-link text-primary"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="small text-muted text-uppercase fw-semibold">URL Slug</div>
                                    <code class="text-dark">/landing/{{ $landingPage->slug }}</code>
                                </div>
                            </div>

                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <div class="bg-light rounded-2 p-2">
                                        <i class="fas fa-search text-primary"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="small text-muted text-uppercase fw-semibold">Meta Title</div>
                                    <div class="text-dark">{{ $landingPage->meta_title ?: 'Using page title' }}</div>
                                </div>
                            </div>

                            @if($landingPage->has_quiz)
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <div class="bg-light rounded-2 p-2">
                                            <i class="fas fa-question-circle text-info"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="small text-muted text-uppercase fw-semibold">Quiz Management</div>
                                        <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-cog me-1"></i>Manage Quiz
                                        </a>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <div class="bg-light rounded-2 p-2">
                                        <i class="fas fa-calendar-plus text-primary"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="small text-muted text-uppercase fw-semibold">Created</div>
                                    <div class="text-dark">{{ $landingPage->created_at->format('M j, Y g:i A') }}</div>
                                </div>
                            </div>

                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <div class="bg-light rounded-2 p-2">
                                        <i class="fas fa-edit text-primary"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="small text-muted text-uppercase fw-semibold">Last Updated</div>
                                    <div class="text-dark">{{ $landingPage->updated_at->format('M j, Y g:i A') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($landingPage->meta_description)
                        <div class="mt-4 p-3 bg-light rounded-3">
                            <div class="small text-muted text-uppercase fw-semibold mb-2">Meta Description</div>
                            <p class="mb-0 text-dark">{{ $landingPage->meta_description }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Recent Leads -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center pb-0">
                    <h5 class="card-title mb-0 fw-bold text-dark">
                        <i class="fas fa-users me-2 text-primary"></i>Recent Leads
                    </h5>
                    <a href="{{ route('admin.leads.index', ['landing_page_id' => $landingPage->id]) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-arrow-right me-1"></i>View All
                    </a>
                </div>
                <div class="card-body pt-3">
                    @if($landingPage->leads->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($landingPage->leads as $lead)
                                <div class="list-group-item border-0 px-0 py-3">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="bg-primary bg-gradient rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                <span class="text-white fw-bold small">{{ $lead->initials }}</span>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1 fw-semibold text-dark">{{ $lead->name }}</h6>
                                                    <div class="small text-muted mb-1">
                                                        <i class="fas fa-envelope me-1"></i>{{ $lead->email }}
                                                    </div>
                                                    <div class="small text-muted">
                                                        <i class="fas fa-building me-1"></i>{{ $lead->company_name }}
                                                    </div>
                                                </div>
                                                <div class="text-end">
                                                    @if($lead->quiz_score !== null)
                                                        <span class="badge bg-info bg-gradient px-2 py-1 mb-1">{{ $lead->quiz_score }} pts</span>
                                                    @endif
                                                    <div class="small text-muted">{{ $lead->created_at->format('M j, Y') }}</div>
                                                    <a href="{{ route('admin.leads.show', $lead) }}" class="btn btn-sm btn-outline-primary mt-1">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="fas fa-user-plus fa-3x text-muted"></i>
                            </div>
                            <h6 class="text-muted mb-2">No leads yet</h6>
                            <p class="text-muted small mb-0">Leads will appear here when visitors complete the assessment.</p>
                        </div>
                    @endif
                </div>
            </div>
    </div>

        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="card-title mb-0 fw-bold text-dark">
                        <i class="fas fa-bolt me-2 text-warning"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body pt-3">
                    <div class="d-grid gap-3">
                        <a href="{{ route('admin.landing-pages.edit', $landingPage) }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-edit me-2"></i>Edit Page
                        </a>
                        @if($landingPage->has_quiz)
                            <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-outline-info">
                                <i class="fas fa-question-circle me-2"></i>Manage Quiz
                            </a>
                        @endif
                        <a href="{{ route('admin.leads.index', ['landing_page_id' => $landingPage->id]) }}" class="btn btn-outline-success">
                            <i class="fas fa-users me-2"></i>View All Leads
                        </a>
                        <a href="{{ route('landing-pages.show', $landingPage) }}" target="_blank" class="btn btn-outline-secondary">
                            <i class="fas fa-external-link-alt me-2"></i>Preview Page
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quiz Information -->
            @if($landingPage->quiz)
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0 pb-0">
                        <h5 class="card-title mb-0 fw-bold text-dark">
                            <i class="fas fa-question-circle me-2 text-info"></i>Quiz Information
                        </h5>
                    </div>
                    <div class="card-body pt-3">
                        <div class="row g-3 mb-4">
                            <div class="col-6">
                                <div class="text-center p-3 bg-light rounded-3">
                                    <div class="h4 mb-1 text-dark fw-bold">{{ $landingPage->quiz->questions->count() }}</div>
                                    <div class="small text-muted text-uppercase fw-semibold">Questions</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3 bg-light rounded-3">
                                    <div class="h4 mb-1 text-dark fw-bold">{{ $landingPage->quiz->getTotalPossibleScore() }}</div>
                                    <div class="small text-muted text-uppercase fw-semibold">Max Score</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <div class="bg-light rounded-2 p-2">
                                    <i class="fas fa-tag text-info"></i>
                                </div>
                            </div>
                            <div>
                                <div class="small text-muted text-uppercase fw-semibold">Quiz Title</div>
                                <div class="text-dark fw-semibold">{{ $landingPage->quiz->title }}</div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <div class="bg-light rounded-2 p-2">
                                    <i class="fas fa-{{ $landingPage->quiz->is_active ? 'check-circle text-success' : 'pause-circle text-secondary' }}"></i>
                                </div>
                            </div>
                            <div>
                                <div class="small text-muted text-uppercase fw-semibold">Status</div>
                                <span class="badge {{ $landingPage->quiz->is_active ? 'bg-success' : 'bg-secondary' }} px-2 py-1">
                                    {{ $landingPage->quiz->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>

                        @if($landingPage->quiz->description)
                            <div class="mt-4 p-3 bg-light rounded-3">
                                <div class="small text-muted text-uppercase fw-semibold mb-2">Description</div>
                                <p class="mb-0 text-dark small">{{ $landingPage->quiz->description }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
