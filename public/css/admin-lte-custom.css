/*
* EndpointSync Admin Styles
* AdminLTE v3 customization with EndpointSync branding
* Blue and gray color scheme with orange highlights
*/

/* Admin color variables */
:root {
    /* Blue and Gray Color Palette with Orange Highlights */
    --admin-primary: #3b82f6; /* Blue primary color */
    --admin-primary-light: #60a5fa; /* Lighter blue */
    --admin-primary-dark: #2563eb; /* Darker blue */
    --admin-primary-soft: rgba(59, 130, 246, 0.08); /* Soft blue for backgrounds */

    /* Orange highlight colors */
    --admin-highlight: #ff8c38; /* Orange highlight */
    --admin-highlight-light: #ffa563; /* Lighter orange */
    --admin-highlight-dark: #e67321; /* Darker orange */
    --admin-highlight-soft: rgba(255, 140, 56, 0.08); /* Soft orange for backgrounds */

    /* Gray color palette */
    --admin-gray-50: #f9fafb;
    --admin-gray-100: #f3f4f6;
    --admin-gray-200: #e5e7eb;
    --admin-gray-300: #d1d5db;
    --admin-gray-400: #9ca3af;
    --admin-gray-500: #6b7280;
    --admin-gray-600: #4b5563;
    --admin-gray-700: #374151;
    --admin-gray-800: #1f2937;
    --admin-gray-900: #111827;

    /* Semantic colors */
    --admin-success: #10b981;
    --admin-warning: #f59e0b;
    --admin-danger: #ef4444;
    --admin-info: #3b82f6;

    /* Background and text colors */
    --admin-white: #ffffff;
    --admin-black: #000000;
    --admin-text-primary: var(--admin-gray-900);
    --admin-text-secondary: var(--admin-gray-600);
    --admin-text-muted: var(--admin-gray-500);

    /* Gradients and effects */
    --admin-gradient-blue: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
    --admin-gradient-orange: linear-gradient(135deg, var(--admin-highlight) 0%, var(--admin-highlight-light) 100%);
    --admin-box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
    --admin-box-shadow-hover: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --admin-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Font Family Override */
body {
    font-family: 'Poppins', sans-serif !important;
}

/* AdminLTE Customizations */

/* Sidebar customization */
.main-sidebar {
    background: linear-gradient(180deg, var(--admin-gray-800) 0%, var(--admin-gray-900) 100%) !important;
}

.sidebar-dark-primary .nav-sidebar > .nav-item > .nav-link.active {
    background-color: var(--admin-primary) !important;
    color: var(--admin-white) !important;
}

.sidebar-dark-primary .nav-sidebar > .nav-item > .nav-link:hover {
    background-color: rgba(59, 130, 246, 0.2) !important;
    color: var(--admin-white) !important;
}

/* Brand link customization */
.brand-link {
    background-color: var(--admin-gray-900) !important;
    border-bottom: 1px solid var(--admin-gray-700) !important;
}

/* Logo styling */
.logo-endpoint {
    color: var(--admin-primary) !important;
    font-weight: 700;
}

.logo-sync {
    color: var(--admin-white) !important;
    font-weight: 700;
}

/* Badge styling */
.badge-warning {
    background-color: var(--admin-highlight) !important;
    color: var(--admin-white) !important;
}

/* Navbar customization */
.main-header.navbar {
    background-color: var(--admin-white) !important;
    border-bottom: 1px solid var(--admin-gray-200) !important;
}

/* Content wrapper */
.content-wrapper {
    background-color: var(--admin-gray-50) !important;
}

/* Cards */
.card {
    box-shadow: var(--admin-box-shadow) !important;
    border: none !important;
    border-radius: 0.5rem !important;
}

.card-header {
    background-color: var(--admin-white) !important;
    border-bottom: 1px solid var(--admin-gray-200) !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--admin-primary) !important;
    border-color: var(--admin-primary) !important;
}

.btn-primary:hover {
    background-color: var(--admin-primary-dark) !important;
    border-color: var(--admin-primary-dark) !important;
}

.btn-warning {
    background-color: var(--admin-highlight) !important;
    border-color: var(--admin-highlight) !important;
    color: var(--admin-white) !important;
}

.btn-warning:hover {
    background-color: var(--admin-highlight-dark) !important;
    border-color: var(--admin-highlight-dark) !important;
    color: var(--admin-white) !important;
}

/* Info boxes */
.info-box {
    box-shadow: var(--admin-box-shadow) !important;
    border-radius: 0.5rem !important;
}

.info-box-icon {
    border-radius: 0.5rem 0 0 0.5rem !important;
}

.bg-info {
    background-color: var(--admin-primary) !important;
}

.bg-success {
    background-color: var(--admin-success) !important;
}

.bg-warning {
    background-color: var(--admin-highlight) !important;
}

.bg-danger {
    background-color: var(--admin-danger) !important;
}

/* Small boxes */
.small-box {
    border-radius: 0.5rem !important;
    box-shadow: var(--admin-box-shadow) !important;
}

.small-box > .inner {
    padding: 15px !important;
}

.small-box .icon {
    top: 10px !important;
    right: 15px !important;
}

/* Tables */
.table {
    background-color: var(--admin-white) !important;
}

.table th {
    border-top: none !important;
    border-bottom: 2px solid var(--admin-gray-200) !important;
    color: var(--admin-gray-700) !important;
    font-weight: 600 !important;
}

/* Alerts */
.alert {
    border: none !important;
    border-radius: 0.5rem !important;
    box-shadow: var(--admin-box-shadow) !important;
}

/* Footer */
.main-footer {
    background-color: var(--admin-white) !important;
    border-top: 1px solid var(--admin-gray-200) !important;
    color: var(--admin-gray-600) !important;
}

/* User panel */
.user-panel .info a {
    color: var(--admin-gray-300) !important;
}

/* Dropdown menus */
.dropdown-menu {
    border: none !important;
    box-shadow: var(--admin-box-shadow) !important;
    border-radius: 0.5rem !important;
}

/* Form controls */
.form-control {
    border: 1px solid var(--admin-gray-300) !important;
    border-radius: 0.375rem !important;
}

.form-control:focus {
    border-color: var(--admin-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25) !important;
}

/* Pagination */
.page-link {
    color: var(--admin-primary) !important;
    border: 1px solid var(--admin-gray-300) !important;
}

.page-link:hover {
    color: var(--admin-primary-dark) !important;
    background-color: var(--admin-gray-100) !important;
    border-color: var(--admin-gray-300) !important;
}

.page-item.active .page-link {
    background-color: var(--admin-primary) !important;
    border-color: var(--admin-primary) !important;
}

/* Custom utility classes */
.bg-primary-soft {
    background-color: var(--admin-primary-soft) !important;
}

.bg-highlight-soft {
    background-color: var(--admin-highlight-soft) !important;
}

.text-primary {
    color: var(--admin-primary) !important;
}

.text-highlight {
    color: var(--admin-highlight) !important;
}

/* Breadcrumb styling */
.breadcrumb {
    background-color: transparent !important;
    margin-bottom: 0 !important;
    padding: 0 !important;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: var(--admin-gray-500) !important;
}

.breadcrumb-item a {
    color: var(--admin-primary) !important;
    text-decoration: none !important;
}

.breadcrumb-item a:hover {
    color: var(--admin-primary-dark) !important;
}

.breadcrumb-item.active {
    color: var(--admin-gray-600) !important;
}

/* Content header styling */
.content-header {
    padding: 15px 0 !important;
}

.content-header h1 {
    font-size: 1.8rem !important;
    font-weight: 600 !important;
    color: var(--admin-gray-800) !important;
    margin: 0 !important;
}

/* Card tools */
.card-tools .btn {
    font-size: 0.875rem !important;
}

/* Badge improvements */
.badge {
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    padding: 0.375rem 0.75rem !important;
}

.badge-primary {
    background-color: var(--admin-primary) !important;
}

.badge-info {
    background-color: var(--admin-info) !important;
}

.badge-success {
    background-color: var(--admin-success) !important;
}

.badge-warning {
    background-color: var(--admin-highlight) !important;
}

.badge-danger {
    background-color: var(--admin-danger) !important;
}

.badge-secondary {
    background-color: var(--admin-gray-500) !important;
}

/* Button group improvements */
.btn-group .btn {
    border-radius: 0.25rem !important;
    margin-right: 2px !important;
}

.btn-group .btn:last-child {
    margin-right: 0 !important;
}

/* Table improvements */
.table td {
    vertical-align: middle !important;
    padding: 0.75rem !important;
}

.table th {
    font-size: 0.875rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    padding: 1rem 0.75rem !important;
}

/* Empty state styling */
.text-center .fa-3x {
    opacity: 0.3 !important;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .content-wrapper {
        margin-left: 0 !important;
    }

    .main-sidebar {
        margin-left: -250px;
    }

    .sidebar-open .main-sidebar {
        margin-left: 0;
    }

    .content-header h1 {
        font-size: 1.5rem !important;
    }

    .btn-group {
        display: flex !important;
        flex-wrap: wrap !important;
    }

    .btn-group .btn {
        margin-bottom: 2px !important;
    }
}
